/**
 * @file mutex_debug.c
 * @brief 互斥锁竞态问题诊断工具
 * <AUTHOR> Assistant
 * @date 2024
 */

#include "FreeRTOS.h"
#include "task.h"
#include "semphr.h"
#include <stdio.h>
#include <string.h>

/* 互斥锁调试信息结构 */
typedef struct {
    SemaphoreHandle_t mutex;
    char name[16];
    TaskHandle_t owner;
    uint32_t take_count;
    uint32_t give_count;
    uint32_t max_wait_time;
    uint32_t total_wait_time;
    uint32_t contention_count;
    bool is_monitoring;
} mutex_debug_info_t;

/* 最大监控的互斥锁数量 */
#define MAX_MONITORED_MUTEXES 8

/* 全局调试信息数组 */
static mutex_debug_info_t mutex_debug_list[MAX_MONITORED_MUTEXES];
static uint32_t monitored_mutex_count = 0;

/**
 * @brief 注册要监控的互斥锁
 * @param mutex 互斥锁句柄
 * @param name 互斥锁名称
 * @return pdTRUE: 成功, pdFALSE: 失败
 */
BaseType_t register_mutex_for_debug(SemaphoreHandle_t mutex, const char* name)
{
    if (monitored_mutex_count >= MAX_MONITORED_MUTEXES || mutex == NULL)
    {
        return pdFALSE;
    }
    
    mutex_debug_info_t* info = &mutex_debug_list[monitored_mutex_count];
    
    info->mutex = mutex;
    strncpy(info->name, name ? name : "Unknown", sizeof(info->name) - 1);
    info->name[sizeof(info->name) - 1] = '\0';
    info->owner = NULL;
    info->take_count = 0;
    info->give_count = 0;
    info->max_wait_time = 0;
    info->total_wait_time = 0;
    info->contention_count = 0;
    info->is_monitoring = true;
    
    monitored_mutex_count++;
    
    printf("[MUTEX_DEBUG] Registered mutex '%s' for monitoring\r\n", info->name);
    return pdTRUE;
}

/**
 * @brief 查找互斥锁调试信息
 * @param mutex 互斥锁句柄
 * @return 调试信息指针，NULL表示未找到
 */
static mutex_debug_info_t* find_mutex_debug_info(SemaphoreHandle_t mutex)
{
    for (uint32_t i = 0; i < monitored_mutex_count; i++)
    {
        if (mutex_debug_list[i].mutex == mutex && mutex_debug_list[i].is_monitoring)
        {
            return &mutex_debug_list[i];
        }
    }
    return NULL;
}

/**
 * @brief 安全的互斥锁获取（带调试）
 * @param mutex 互斥锁句柄
 * @param timeout 超时时间
 * @return pdTRUE: 成功, pdFALSE: 失败
 */
BaseType_t debug_mutex_take(SemaphoreHandle_t mutex, TickType_t timeout)
{
    mutex_debug_info_t* info = find_mutex_debug_info(mutex);
    TaskHandle_t current_task = xTaskGetCurrentTaskHandle();
    TickType_t start_time = xTaskGetTickCount();
    BaseType_t result;
    
    /* 检查是否在监控列表中 */
    if (info != NULL)
    {
        /* 检查是否是同一任务重复获取 */
        if (info->owner == current_task)
        {
            printf("[MUTEX_DEBUG] WARNING: Task '%s' trying to take mutex '%s' again!\r\n",
                   pcTaskGetName(current_task), info->name);
            info->contention_count++;
        }
        
        printf("[MUTEX_DEBUG] Task '%s' attempting to take mutex '%s'\r\n",
               pcTaskGetName(current_task), info->name);
    }
    
    /* 尝试获取互斥锁 */
    result = xSemaphoreTake(mutex, timeout);
    
    if (info != NULL)
    {
        TickType_t wait_time = xTaskGetTickCount() - start_time;
        
        if (result == pdTRUE)
        {
            info->owner = current_task;
            info->take_count++;
            info->total_wait_time += wait_time;
            
            if (wait_time > info->max_wait_time)
            {
                info->max_wait_time = wait_time;
            }
            
            printf("[MUTEX_DEBUG] Task '%s' acquired mutex '%s' (wait: %d ticks)\r\n",
                   pcTaskGetName(current_task), info->name, (int)wait_time);
        }
        else
        {
            printf("[MUTEX_DEBUG] Task '%s' FAILED to acquire mutex '%s' (timeout: %d ticks)\r\n",
                   pcTaskGetName(current_task), info->name, (int)timeout);
            info->contention_count++;
        }
    }
    
    return result;
}

/**
 * @brief 安全的互斥锁释放（带调试）
 * @param mutex 互斥锁句柄
 * @return pdTRUE: 成功, pdFALSE: 失败
 */
BaseType_t debug_mutex_give(SemaphoreHandle_t mutex)
{
    mutex_debug_info_t* info = find_mutex_debug_info(mutex);
    TaskHandle_t current_task = xTaskGetCurrentTaskHandle();
    BaseType_t result;
    
    /* 检查是否在监控列表中 */
    if (info != NULL)
    {
        /* 检查是否是锁的拥有者 */
        if (info->owner != current_task)
        {
            printf("[MUTEX_DEBUG] ERROR: Task '%s' trying to release mutex '%s' owned by '%s'!\r\n",
                   pcTaskGetName(current_task), info->name,
                   info->owner ? pcTaskGetName(info->owner) : "None");
            return pdFALSE;
        }
        
        printf("[MUTEX_DEBUG] Task '%s' releasing mutex '%s'\r\n",
               pcTaskGetName(current_task), info->name);
    }
    
    /* 释放互斥锁 */
    result = xSemaphoreGive(mutex);
    
    if (info != NULL)
    {
        if (result == pdTRUE)
        {
            info->owner = NULL;
            info->give_count++;
            
            printf("[MUTEX_DEBUG] Task '%s' released mutex '%s' successfully\r\n",
                   pcTaskGetName(current_task), info->name);
        }
        else
        {
            printf("[MUTEX_DEBUG] ERROR: Task '%s' FAILED to release mutex '%s'\r\n",
                   pcTaskGetName(current_task), info->name);
        }
    }
    
    return result;
}

/**
 * @brief 打印互斥锁统计信息
 */
void print_mutex_debug_stats(void)
{
    printf("[MUTEX_DEBUG] === Mutex Debug Statistics ===\r\n");
    
    for (uint32_t i = 0; i < monitored_mutex_count; i++)
    {
        mutex_debug_info_t* info = &mutex_debug_list[i];
        
        if (!info->is_monitoring)
            continue;
        
        printf("[MUTEX_DEBUG] Mutex: %-12s | Owner: %-12s | Take: %4d | Give: %4d | Contention: %4d\r\n",
               info->name,
               info->owner ? pcTaskGetName(info->owner) : "None",
               (int)info->take_count,
               (int)info->give_count,
               (int)info->contention_count);
        
        if (info->take_count > 0)
        {
            printf("[MUTEX_DEBUG]   Max Wait: %4d ticks | Avg Wait: %4d ticks\r\n",
                   (int)info->max_wait_time,
                   (int)(info->total_wait_time / info->take_count));
        }
        
        /* 检查不平衡 */
        if (info->take_count != info->give_count)
        {
            printf("[MUTEX_DEBUG]   WARNING: Unbalanced take/give count!\r\n");
        }
    }
    
    printf("[MUTEX_DEBUG] === End of Statistics ===\r\n");
}

/**
 * @brief 检查互斥锁死锁情况
 */
void check_mutex_deadlock(void)
{
    printf("[MUTEX_DEBUG] === Deadlock Detection ===\r\n");
    
    for (uint32_t i = 0; i < monitored_mutex_count; i++)
    {
        mutex_debug_info_t* info = &mutex_debug_list[i];
        
        if (!info->is_monitoring)
            continue;
        
        if (info->owner != NULL)
        {
            /* 检查任务状态 */
            TaskStatus_t task_status;
            vTaskGetInfo(info->owner, &task_status, pdTRUE, eInvalid);
            
            printf("[MUTEX_DEBUG] Mutex '%s' held by task '%s' (state: %s)\r\n",
                   info->name,
                   task_status.pcTaskName,
                   (task_status.eCurrentState == eReady) ? "Ready" :
                   (task_status.eCurrentState == eBlocked) ? "Blocked" :
                   (task_status.eCurrentState == eSuspended) ? "Suspended" :
                   (task_status.eCurrentState == eRunning) ? "Running" : "Unknown");
            
            /* 如果任务被阻塞且持有锁，可能存在死锁 */
            if (task_status.eCurrentState == eBlocked)
            {
                printf("[MUTEX_DEBUG]   POTENTIAL DEADLOCK: Task is blocked while holding mutex!\r\n");
            }
        }
    }
    
    printf("[MUTEX_DEBUG] === End of Deadlock Detection ===\r\n");
}

/**
 * @brief 重置互斥锁调试统计
 */
void reset_mutex_debug_stats(void)
{
    for (uint32_t i = 0; i < monitored_mutex_count; i++)
    {
        mutex_debug_info_t* info = &mutex_debug_list[i];
        
        info->take_count = 0;
        info->give_count = 0;
        info->max_wait_time = 0;
        info->total_wait_time = 0;
        info->contention_count = 0;
    }
    
    printf("[MUTEX_DEBUG] Statistics reset\r\n");
}

/**
 * @brief 互斥锁调试主函数
 */
void mutex_debug_main(void)
{
    printf("[MUTEX_DEBUG] Starting mutex debug analysis...\r\n");
    
    /* 打印统计信息 */
    print_mutex_debug_stats();
    
    /* 检查死锁 */
    check_mutex_deadlock();
    
    printf("[MUTEX_DEBUG] Mutex debug analysis completed\r\n");
}
