/**
 * @file stack_monitor.c
 * @brief FreeRTOS栈使用情况监控工具
 * <AUTHOR> Assistant
 * @date 2024
 */

#include "FreeRTOS.h"
#include "task.h"
#include "elog.h"
#include <string.h>
#include <stdio.h>

/* 日志标签 */
#define STACK_MONITOR_TAG "STACK_MON"

/**
 * @brief 获取任务栈使用情况
 * @param xTask 任务句柄，NULL表示当前任务
 * @return 剩余栈空间（以字为单位）
 */
UBaseType_t get_task_stack_high_water_mark(TaskHandle_t xTask)
{
    return uxTaskGetStackHighWaterMark(xTask);
}

/**
 * @brief 打印单个任务的栈使用情况
 * @param xTask 任务句柄
 * @param pcTaskName 任务名称
 */
void print_task_stack_usage(TaskHandle_t xTask, const char* pcTaskName)
{
    UBaseType_t uxHighWaterMark;
    UBaseType_t uxStackSize;
    UBaseType_t uxUsedStack;
    float fUsagePercent;
    
    /* 获取栈高水位标记（剩余最小栈空间） */
    uxHighWaterMark = uxTaskGetStackHighWaterMark(xTask);
    
    /* 获取任务信息 */
    TaskStatus_t xTaskDetails;
    vTaskGetInfo(xTask, &xTaskDetails, pdTRUE, eInvalid);
    
    /* 计算栈使用情况 */
    uxStackSize = xTaskDetails.usStackHighWaterMark + uxHighWaterMark;
    uxUsedStack = uxStackSize - uxHighWaterMark;
    fUsagePercent = ((float)uxUsedStack / (float)uxStackSize) * 100.0f;
    
    /* 输出栈使用情况 */
    printf("[%s] Task: %-20s | Stack Size: %4d | Used: %4d | Free: %4d | Usage: %5.1f%%\r\n",
          STACK_MONITOR_TAG,
          pcTaskName ? pcTaskName : "Unknown",
          (int)uxStackSize * 4,  /* 转换为字节 */
          (int)uxUsedStack * 4,  /* 转换为字节 */
          (int)uxHighWaterMark * 4,  /* 转换为字节 */
          fUsagePercent);

    /* 栈使用率警告 */
    if (fUsagePercent > 90.0f)
    {
        printf("[%s] WARNING: Task '%s' stack usage is %.1f%% - CRITICAL!\r\n",
              STACK_MONITOR_TAG,
              pcTaskName ? pcTaskName : "Unknown", fUsagePercent);
    }
    else if (fUsagePercent > 80.0f)
    {
        printf("[%s] WARNING: Task '%s' stack usage is %.1f%% - HIGH!\r\n",
              STACK_MONITOR_TAG,
              pcTaskName ? pcTaskName : "Unknown", fUsagePercent);
    }
}

/**
 * @brief 打印所有任务的栈使用情况
 */
void print_all_tasks_stack_usage(void)
{
    UBaseType_t uxArraySize;
    TaskStatus_t *pxTaskStatusArray;
    volatile UBaseType_t uxCurrentNumberOfTasks;
    UBaseType_t x;
    
    printf("[%s] === FreeRTOS Tasks Stack Usage Report ===\r\n", STACK_MONITOR_TAG);

    /* 获取当前任务数量 */
    uxCurrentNumberOfTasks = uxTaskGetNumberOfTasks();

    /* 分配内存存储任务状态 */
    pxTaskStatusArray = pvPortMalloc(uxCurrentNumberOfTasks * sizeof(TaskStatus_t));

    if (pxTaskStatusArray != NULL)
    {
        /* 获取所有任务的状态信息 */
        uxArraySize = uxTaskGetSystemState(pxTaskStatusArray, uxCurrentNumberOfTasks, NULL);

        /* 打印每个任务的栈使用情况 */
        for (x = 0; x < uxArraySize; x++)
        {
            print_task_stack_usage(pxTaskStatusArray[x].xHandle, pxTaskStatusArray[x].pcTaskName);
        }

        /* 释放内存 */
        vPortFree(pxTaskStatusArray);
    }
    else
    {
        printf("[%s] ERROR: Failed to allocate memory for task status array\r\n", STACK_MONITOR_TAG);
    }

    printf("[%s] === End of Stack Usage Report ===\r\n", STACK_MONITOR_TAG);
}

/**
 * @brief 检查特定任务的栈使用情况
 * @param pcTaskName 任务名称
 * @return pdTRUE: 栈使用正常, pdFALSE: 栈使用异常
 */
BaseType_t check_task_stack_safety(const char* pcTaskName)
{
    TaskHandle_t xTaskHandle;
    UBaseType_t uxHighWaterMark;
    
    /* 根据任务名称查找任务句柄 */
    xTaskHandle = xTaskGetHandle(pcTaskName);
    
    if (xTaskHandle == NULL)
    {
        printf("[%s] ERROR: Task '%s' not found\r\n", STACK_MONITOR_TAG, pcTaskName);
        return pdFALSE;
    }

    /* 获取栈高水位标记 */
    uxHighWaterMark = uxTaskGetStackHighWaterMark(xTaskHandle);

    /* 检查剩余栈空间是否足够（少于64字节认为危险） */
    if (uxHighWaterMark < 16)  /* 16 words = 64 bytes */
    {
        printf("[%s] CRITICAL: Task '%s' has only %d bytes of stack remaining!\r\n",
              STACK_MONITOR_TAG, pcTaskName, (int)uxHighWaterMark * 4);
        return pdFALSE;
    }
    else if (uxHighWaterMark < 32)  /* 32 words = 128 bytes */
    {
        printf("[%s] WARNING: Task '%s' has only %d bytes of stack remaining!\r\n",
              STACK_MONITOR_TAG, pcTaskName, (int)uxHighWaterMark * 4);
    }
    
    return pdTRUE;
}

/**
 * @brief 栈监控任务（可选）
 * @param pvParameters 任务参数
 */
void stack_monitor_task(void *pvParameters)
{
    const TickType_t xDelay = pdMS_TO_TICKS(10000);  /* 10秒检查一次 */
    
    printf("[%s] Stack Monitor Task Started\r\n", STACK_MONITOR_TAG);

    for (;;)
    {
        /* 打印所有任务的栈使用情况 */
        print_all_tasks_stack_usage();

        /* 延时 */
        vTaskDelay(xDelay);
    }
}

/**
 * @brief 创建栈监控任务
 * @return pdPASS: 创建成功, pdFAIL: 创建失败
 */
BaseType_t create_stack_monitor_task(void)
{
    BaseType_t xReturn;
    
    xReturn = xTaskCreate(
        stack_monitor_task,           /* 任务函数 */
        "StackMonitor",              /* 任务名称 */
        256,                         /* 栈大小 */
        NULL,                        /* 任务参数 */
        1,                           /* 任务优先级 */
        NULL                         /* 任务句柄 */
    );
    
    if (xReturn == pdPASS)
    {
        printf("[%s] Stack Monitor Task created successfully\r\n", STACK_MONITOR_TAG);
    }
    else
    {
        printf("[%s] ERROR: Failed to create Stack Monitor Task\r\n", STACK_MONITOR_TAG);
    }
    
    return xReturn;
}
