@startuml
title 电网同步采样+谐波相位分析流程

start

:市电信号（50Hz）输入;
:LM211过零检测输出方波;
:STM32输入捕获记录过零时间 t_zero;

:启动定时器 TIMy @ 6400Hz;
repeat
    :触发一次采样（AD7606 CONVST）;
    :等待BUSY=低;
    :读取1点数据存入 adc_buffer[];
    :记录当前采样时间 t_sample[n];
repeat while (n < 1024)

:执行窗函数处理（如 Nuttall）;
:进行FFT变换，获得各bin复数值;
:使用三谱线插值获取精确幅度和相位 φ_FFT;

:根据 t_sample[0] 与 t_zero 计算 θ = 2π·Δt / T_grid;
:修正相位 φ_real = φ_FFT - θ;

:输出频率、幅度、相位结果;

stop
@enduml
